#include "dac_app.h"
#include "math.h"


// --- 私有变量 ---
static uint16_t CurrentWave[WAVE_SAMPLES];				// 存储正弦波数据的数组
static waveform_t current_waveform = WAVEFORM_SINE;		// 当前输出的波形类型
//static uint32_t current_freq_hz = 1000;					// 当前波形频率 (Hz)
static uint16_t Current_amplitude_mv = DAC_VREF_MV;		// 当前峰值幅度 (mV)
static float duty_cycle  = 0.5;							// 方波占空比 (0.0-1.0)


// --- 正弦波生成函数 (最低电平0) ---
/**
 * @brief 生成正弦波查找表（最低电平0）
 * @param amplitude: 正弦波的峰值电平 (0-4095)
 * @retval None
 */
void Generate_Sine_Wave(uint16_t amplitude)
{
	// 确保幅度不超过DAC范围
	if(amplitude > WAVE_MAX_VALUE) amplitude = WAVE_MAX_VALUE;
	
	// 计算每个采样点之间的角度步进 (2*PI / samples)
	float step = 2.0f * 3.14159f / WAVE_SAMPLES;
	for(uint32_t i = 0; i < WAVE_SAMPLES; i++)
	{
		// 计算当前点的正弦值 (-1.0 到 1.0)
		float sine_value = sinf(i * step);

		// 将正弦值从(-1.0, 1.0)映射到(0, amplitude)
		// 1. 调整到(0, 1)范围: (sine_value + 1.0) / 2.0
		// 2. 缩放到(0, amplitude)范围
		float normalized = (sine_value + 1.0f) * 0.5f;
		CurrentWave[i] = (uint16_t)(normalized * amplitude);
		
		// 确保值在有效范围内
		if (CurrentWave[i] > WAVE_MAX_VALUE) CurrentWave[i] = WAVE_MAX_VALUE;
	}
}


// --- 方波生成函数 (最低电平0) ---
/**
 * @brief 生成方波查找表（最低电平0）
 * @param amplitude: 方波的峰值电平 (0-4095)
 * @retval None
 */
void Generate_Square_Wave(uint16_t amplitude)
{
	// 确保幅度不超过DAC范围
	if(amplitude > WAVE_MAX_VALUE) amplitude = WAVE_MAX_VALUE;
  
	// 计算高电平的采样点数
	uint32_t high_samples = (uint32_t)(WAVE_SAMPLES * duty_cycle);
  
	for(uint32_t i = 0; i < WAVE_SAMPLES; i++)
	{
		// 根据占空比设置电平值：高电平 = amplitude，低电平 = 0
		CurrentWave[i] = (i < high_samples) ? amplitude : 0;
	}
}


// --- 三角波生成函数 (最低电平0) ---
/**
 * @brief 生成三角波查找表（最低电平0）
 * @param amplitude: 三角波的峰值电平 (0-4095)
 * @retval None
 */
void Generate_Triangle_Wave(uint16_t amplitude)
{
	// 确保幅度不超过DAC范围
	if(amplitude > WAVE_MAX_VALUE) amplitude = WAVE_MAX_VALUE;
  	
	uint32_t half_samples = WAVE_SAMPLES / 2;
	float delta_up = (float)WAVE_MAX_VALUE / half_samples;
	float delta_down = (float)WAVE_MAX_VALUE / (WAVE_SAMPLES - half_samples);

	for (uint32_t i = 0; i < half_samples; i++)
	{
		CurrentWave[i] = (uint16_t)(i * delta_up);
	}
	for (uint32_t i = half_samples; i < WAVE_SAMPLES; i++)
	{
		CurrentWave[i] = WAVE_MAX_VALUE - (uint16_t)((i - half_samples) * delta_down);
	}

}


// --- 选择函数实现 ---
static void generate_waveform(void)
{ // 根据当前设置生成波形
    switch (current_waveform)
    {
    case WAVEFORM_SINE:
		Generate_Sine_Wave(Current_amplitude_mv);
    break;
    case WAVEFORM_SQUARE:
		Generate_Square_Wave(Current_amplitude_mv);
	break;
    case WAVEFORM_TRIANGLE:
		Generate_Triangle_Wave(Current_amplitude_mv);
	break;
    default:
		Generate_Sine_Wave(Current_amplitude_mv);
	break; // 默认为正弦波
    }
}


static HAL_StatusTypeDef start_dac_dma(void)	// 启动 DAC DMA 和定时器
{

    HAL_StatusTypeDef status_dac = HAL_DAC_Start_DMA(&hdac, DAC_CHANNEL_1, (uint32_t *)CurrentWave, WAVE_SAMPLES, DAC_ALIGN_12B_R);
    HAL_StatusTypeDef status_tim_start = HAL_TIM_Base_Start(&htim4);

    return (status_dac == HAL_OK && status_tim_start == HAL_OK) ? HAL_OK : HAL_ERROR;
}


static HAL_StatusTypeDef stop_dac_dma(void)		// 停止 DAC DMA 和定时器
{
    HAL_StatusTypeDef status1 = HAL_DAC_Stop_DMA(&hdac, DAC_CHANNEL_1);
    HAL_TIM_Base_Stop(&htim4);					// 直接使用 Stop，不检查返回值简化
    return status1;								// 主要关心 DAC DMA 是否成功停止
}


// --- 初始化函数 (在 main 函数或外设初始化后调用) ---
void dac_wave_init(void)
{
    // 生成查找表数据
    generate_waveform();
	// 启动定时器和 DMA 输出
    start_dac_dma(); 
}


HAL_StatusTypeDef dac_app_set_waveform(waveform_t type) // 设置波形类型
{
    if (stop_dac_dma() != HAL_OK)
        return HAL_ERROR;
    current_waveform = type;
    generate_waveform(); // 使用新的类型和当前的幅度重新生成波形
    // 频率不变，定时器周期不需要重新计算，只需重新启动
    return start_dac_dma();
}






