--cpu=Cortex-M4.fp.sp
"adda_demo_01\startup_stm32f429xx.o"
"adda_demo_01\main.o"
"adda_demo_01\gpio.o"
"adda_demo_01\adc.o"
"adda_demo_01\dac.o"
"adda_demo_01\dma.o"
"adda_demo_01\tim.o"
"adda_demo_01\usart.o"
"adda_demo_01\stm32f4xx_it.o"
"adda_demo_01\stm32f4xx_hal_msp.o"
"adda_demo_01\stm32f4xx_hal_adc.o"
"adda_demo_01\stm32f4xx_hal_adc_ex.o"
"adda_demo_01\stm32f4xx_ll_adc.o"
"adda_demo_01\stm32f4xx_hal_rcc.o"
"adda_demo_01\stm32f4xx_hal_rcc_ex.o"
"adda_demo_01\stm32f4xx_hal_flash.o"
"adda_demo_01\stm32f4xx_hal_flash_ex.o"
"adda_demo_01\stm32f4xx_hal_flash_ramfunc.o"
"adda_demo_01\stm32f4xx_hal_gpio.o"
"adda_demo_01\stm32f4xx_hal_dma_ex.o"
"adda_demo_01\stm32f4xx_hal_dma.o"
"adda_demo_01\stm32f4xx_hal_pwr.o"
"adda_demo_01\stm32f4xx_hal_pwr_ex.o"
"adda_demo_01\stm32f4xx_hal_cortex.o"
"adda_demo_01\stm32f4xx_hal.o"
"adda_demo_01\stm32f4xx_hal_exti.o"
"adda_demo_01\stm32f4xx_hal_dac.o"
"adda_demo_01\stm32f4xx_hal_dac_ex.o"
"adda_demo_01\stm32f4xx_hal_tim.o"
"adda_demo_01\stm32f4xx_hal_tim_ex.o"
"adda_demo_01\stm32f4xx_hal_uart.o"
"adda_demo_01\system_stm32f4xx.o"
"adda_demo_01\ringbuffer.o"
"adda_demo_01\ebtn.o"
"adda_demo_01\btn_app.o"
"adda_demo_01\led_app.o"
"adda_demo_01\scheduler.o"
"adda_demo_01\usart_app.o"
"adda_demo_01\adc_app.o"
"adda_demo_01\dac_app.o"
--library_type=microlib --strict --scatter "ADDA_demo_01\ADDA_demo_01.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "ADDA_demo_01.map" -o ADDA_demo_01\ADDA_demo_01.axf