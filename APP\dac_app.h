#ifndef DAC_APP_H
#define DAC_APP_H

#include "mydefine.h"
#define DAC_RESOLUTION_BITS 12                         // DAC 分辨率 (位数)
#define WAVE_SAMPLES 100				// 每个波形周期的采样点数 (影响内存和最高频率)
#define WAVE_MAX_VALUE ((1 << DAC_RESOLUTION_BITS) - 1)	// DAC 最大数字值 (例如 12位 -> 4095)
#define DAC_VREF_MV 3300				// DAC 参考电压 (毫伏)


// --- 波形类型枚举 ---
typedef enum
{
    WAVEFORM_SINE,    // 正弦波
    WAVEFORM_SQUARE,  // 方波
    WAVEFORM_TRIANGLE // 三角波
} waveform_t;


// --- 函数原型 ---
/**
 * @brief 初始化 DAC 应用库
 * @param None
 * @retval None
 */
void dac_wave_init(void);


/**
 * @brief 设置输出波形类型
 * @param type: 波形类型 (dac_waveform_t 枚举)
 * @retval HAL_StatusTypeDef: 操作状态
 */
HAL_StatusTypeDef dac_app_set_waveform(waveform_t type); 


#endif




