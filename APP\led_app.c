#include "led_app.h"
#include "math.h"

#define SINE_TABLE_SIZE 100  // 正弦表点数 ———— 更新次数
#define PWM_PERIOD      9999  // ARR值

uint16_t sineWave[SINE_TABLE_SIZE];  // 正弦波占空比表
uint8_t waveIndex = 0;               // 当前索引


uint8_t ucLed[6] = {0,0,0,0,0,0};  // LED 状态数组
uint8_t breath_enable = 0;

/**
 * @brief 显示或关闭Led
 * @param ucLed Led数据储存数组
 */
void led_disp(uint8_t *ucLed)
{
    uint8_t temp = 0x00;
    static uint8_t temp_old = 0xff;

    for (int i = 0; i < 6; i++)
    {
        if (ucLed[i]) temp |= (1<<i); // 将第i位置1
    }
	
    if (temp_old != temp)
    {
        HAL_GPIO_WritePin(GPIOD, GPIO_PIN_8 , (temp & 0x01) ? GPIO_PIN_SET : GPIO_PIN_RESET); // LED 0
        HAL_GPIO_WritePin(GPIOD, GPIO_PIN_9 , (temp & 0x02) ? GPIO_PIN_SET : GPIO_PIN_RESET); // LED 1
        HAL_GPIO_WritePin(GPIOD, GPIO_PIN_10, (temp & 0x04) ? GPIO_PIN_SET : GPIO_PIN_RESET); // LED 2
        HAL_GPIO_WritePin(GPIOD, GPIO_PIN_11, (temp & 0x08) ? GPIO_PIN_SET : GPIO_PIN_RESET); // LED 3
        HAL_GPIO_WritePin(GPIOD, GPIO_PIN_12, (temp & 0x10) ? GPIO_PIN_SET : GPIO_PIN_RESET); // LED 4
        HAL_GPIO_WritePin(GPIOD, GPIO_PIN_13, (temp & 0x20) ? GPIO_PIN_SET : GPIO_PIN_RESET); // LED 5
        temp_old = temp;
    }
}

/**
 * @brief 呼吸流水灯 - 软件PWM模拟
 *		  函数调用周期(约为)1ms -> PWM周期10ms
 * 		  计数次数1000 -> 函数调用周期(约为)1ms * 1000 -> 单个灯呼吸周期 = 流水周期 = 1s

 * @analysis 为什么可以这样软件模拟？
 *		     本质核心在于：实际上更新了1000次占空比 每10次为一个PWM周期
 * 			 			  但 近似认为在一个PWM周期里面 占空比(即亮度brightness)不变
 * 		     原因: PWM周期和呼吸周期对比有好几个数量级的差距 PWM周期内的占空比变化可以忽略不计
 */
void led_task(void)
{
	if(breath_enable == 1)
	{
		// 呼吸流水灯相关变量
		static uint32_t breathCounter = 0;      // 全局呼吸计时器
		static uint8_t pwmCounter = 0;          // 软件PWM计数器 (所有LED共用)
		static const uint16_t breathPeriod = 1000; // 单个灯呼吸效果的周期 (调慢一点，看得清楚)
		static const uint8_t pwmMax = 10;       // PWM精度
			
		// 相位差: 一个完整周期(2π)内能容纳 6 个LED (2π / 6 = (π/3))
		static const float phaseShift = 3.14159f / 6.0f;
		// 更新全局呼吸计时器
		breathCounter = (breathCounter + 1) % breathPeriod;

		// 更新PWM计数器
		pwmCounter = (pwmCounter + 1) % pwmMax;

		// 循环为每个LED计算独立的亮度并设置状态
		for(uint8_t i = 0; i < 6; i++) // 遍历所有6个LED
		{
			//计算当前LED的相位角 (angle)
			// (2.0f * 3.14159f * breathCounter) / breathPeriod 是基础角度，随时间变化
			// - i * phaseShift 是为第 i 个LED引入的相位延迟
			float angle = (2.0f * 3.14159f * breathCounter) / breathPeriod - i * phaseShift;

			// 计算原始的正弦值 (-1.0 到 1.0)
			float sinValue = sinf(angle);

			// 增强对比度并调整曲线 (让亮灭更分明，全亮时间更短)
			float enhancedValue = sinValue > 0 ? powf(sinValue, 0.5f) : -powf(sinValue, 2.0f);

			// 进一步压缩亮度曲线，使得只有在接近峰值时才真正达到高亮度
			enhancedValue = enhancedValue > 0.6f ? enhancedValue : enhancedValue * 0.6f;
		
			// 将处理后的 enhancedValue (-1 到 1 之间) 映射到 0 到 pwmMax 的亮度值
			uint8_t brightness = (uint8_t)((enhancedValue + 1.0f) * pwmMax / 2.0f);

			// 根据计算出的该LED的亮度，使用PWM逻辑设置其状态
			ucLed[i] = (pwmCounter < brightness) ? 1 : 0;
		}
	}
    // 所有LED的状态都计算完毕后，统一调用 led_disp 更新GPIO
    led_disp(ucLed);
}

