#include "ebtn.h" // 包含 ebtn 库头文件
#include "btn_app.h"

static waveform_t current_wave_type = WAVEFORM_SINE;

typedef enum
{
	BUTTON_1 = 1,
	BUTTON_2,
	BUTTON_3,
	BUTTON_4,
	BUTTON_5,
	BUTTON_6,

	COMBO_BUTTON_copy = 101,
	COMBO_BUTTON_paste,
	COMBO_BUTTON_cut,
	COMBO_BUTTON_breath
} ebtn_button_num;

/* 1. 定义按键参数实例 */
// 参数宏: EBTN_PARAMS_INIT(
//     按下消抖时间ms,
//     释放消抖时间ms,
//     单击有效最短按下时间ms,
//	   单击有效最长按下时间ms,
//     多次单击最大间隔时间ms,
//     长按(KeepAlive)事件周期ms (0禁用),
//     最大连续有效点击次数 (e.g., 1=单击, 2=双击, ...)
// )
const ebtn_btn_param_t key_param_normal = EBTN_PARAMS_INIT(
    20,     // time_debounce: 按下稳定 20ms
    20,     // time_debounce_release: 释放稳定 20ms
    50,     // time_click_pressed_min: 最短单击按下 50ms
    500,    // time_click_pressed_max: 最长单击按下 500ms (超过则不算单击)
    300,    // time_click_multi_max: 多次单击最大间隔 300ms (两次点击间隔超过则重新计数)
    500,    // time_keepalive_period: 长按事件周期 500ms (按下超过 500ms 后，每 500ms 触发一次)
    5       // max_consecutive: 最多支持 5 连击
);


/* 2. 定义静态按键列表 */
// 宏: EBTN_BUTTON_INIT(按键ID, 参数指针)
ebtn_btn_t static_buttons[] = {
    EBTN_BUTTON_INIT(BUTTON_1, &key_param_normal), // KEY1, ID=1, 使用 'key_param_normal' 参数
    EBTN_BUTTON_INIT(BUTTON_2, &key_param_normal),
	EBTN_BUTTON_INIT(BUTTON_3, &key_param_normal),
    EBTN_BUTTON_INIT(BUTTON_4, &key_param_normal),
    EBTN_BUTTON_INIT(BUTTON_5, &key_param_normal),
    EBTN_BUTTON_INIT(BUTTON_6, &key_param_normal),
};


/* 3. 定义静态组合按键列表 */
ebtn_btn_combo_t static_combos[] = {
    EBTN_BUTTON_COMBO_INIT_RAW(COMBO_BUTTON_copy, &key_param_normal, EBTN_EVT_MASK_ONCLICK),    // 假设 KEY1+KEY2 组合键
	EBTN_BUTTON_COMBO_INIT_RAW(COMBO_BUTTON_paste, &key_param_normal, EBTN_EVT_MASK_ONCLICK),    // 假设 KEY1+KEY3 组合键
	EBTN_BUTTON_COMBO_INIT_RAW(COMBO_BUTTON_cut, &key_param_normal, EBTN_EVT_MASK_ONCLICK),     // 假设 KEY1+KEY4 组合键
	EBTN_BUTTON_COMBO_INIT_RAW(COMBO_BUTTON_breath, &key_param_normal, EBTN_EVT_MASK_ONCLICK)     // 假设 KEY1+KEY4 组合键
};


/* 4. 实现获取按键状态的回调函数 */
uint8_t btn_get_state(struct ebtn_btn *btn) {
    // 根据传入的按钮实例中的 key_id 判断是哪个物理按键
    switch (btn->key_id) {
        case 1: // 请求读取 KEY1 的状态
            return (HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_15) == GPIO_PIN_RESET);
        case 2: // 请求读取 KEY2 的状态
            return (HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_13) == GPIO_PIN_RESET);
        case 3: // 请求读取 KEY2 的状态
            return (HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_11) == GPIO_PIN_RESET);
        case 4: // 请求读取 KEY2 的状态
            return (HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_9) == GPIO_PIN_RESET);
        case 5: // 请求读取 KEY2 的状态
            return (HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_7) == GPIO_PIN_RESET);
        case 6: // 请求读取 KEY2 的状态
            return (HAL_GPIO_ReadPin(GPIOB, GPIO_PIN_0) == GPIO_PIN_RESET);

        default:
            // 对于库内部处理组合键等情况，或者未知的 key_id，安全起见返回 0 (未按下)
            return 0;
    }
    // 注意：返回值 1 表示 "活动/按下"，0 表示 "非活动/释放"
}


/* 5. 实现处理按键事件的回调函数 */
void btn_prv_event(struct ebtn_btn *btn, ebtn_evt_t evt) {
	static uint8_t ucLed_temp[6] = {0};
	static uint8_t ucLed_box[6] = {0};
    uint16_t key_id = btn->key_id;                 // 获取触发事件的按键 ID
    uint16_t click_cnt = ebtn_click_get_count(btn); // 获取连击次数 (仅在 ONCLICK 事件时有意义)
    // uint16_t kalive_cnt = ebtn_keepalive_get_count(btn); // 获取长按计数 (仅在 KEEPALIVE 事件时有意义)

    // 根据事件类型进行处理
    if(evt == EBTN_EVT_ONCLICK && click_cnt == 1)
	{
		if(breath_enable == 0)//仅在控制模式下生效，呼吸灯模式不生效
		{
			switch(key_id)
			{
				/*判断独立按键*/
				case BUTTON_1:
					ucLed[0] ^= 1; 
					switch (current_wave_type)
					{
						case WAVEFORM_SINE:
							current_wave_type = WAVEFORM_SQUARE;
							break;
						case WAVEFORM_SQUARE:
							current_wave_type = WAVEFORM_TRIANGLE;
							break;
						case WAVEFORM_TRIANGLE:
							current_wave_type = WAVEFORM_SINE;
							break;
						default:
							current_wave_type = WAVEFORM_SINE;
							break;
					}

					// 设置新的波形类型
					dac_app_set_waveform(current_wave_type);
				break;
				case BUTTON_2:
					ucLed[1] ^= 1; 
				break;
				case BUTTON_3:
					ucLed[2] ^= 1; 
				break;
				case BUTTON_4:
					ucLed[3] ^= 1; 
				break;
				case BUTTON_5:
					ucLed[4] ^= 1; 
				break;
				case BUTTON_6:
					ucLed[5] ^= 1; 
				break;
				
				/*判断组合按键*/
				case COMBO_BUTTON_copy:
						memcpy(ucLed_temp, ucLed, sizeof(ucLed));
				break;
				case COMBO_BUTTON_paste:
						memcpy(ucLed, ucLed_temp, sizeof(ucLed));
				break;
				case COMBO_BUTTON_cut:
						memcpy(ucLed_temp, ucLed, sizeof(ucLed));
						memset(ucLed, 0, sizeof(ucLed));	
				break;
			}
		}
		
		/*组合按键COMBO_BUTTON_breath*/
		if(key_id == COMBO_BUTTON_breath)
		{
			breath_enable ^= 1;
			if(breath_enable == 0)	
				memcpy(ucLed, ucLed_box, sizeof(ucLed));
			else
				memcpy(ucLed_box, ucLed, sizeof(ucLed));
		}
	}
}


/* 5. 实现处理按键事件的回调函数 */
// 初始化 ebtn 库
int app_ebtn_init(void) {
    ebtn_init(
        static_buttons,                	 // 静态按键数组的指针
        EBTN_ARRAY_SIZE(static_buttons), // 静态按键数量 (用宏计算)
        static_combos,                 	 // 静态组合按键数组的指针 (如果没有，传 NULL, 0)
        EBTN_ARRAY_SIZE(static_combos),  // 静态组合按键数量 (如果没有，传 0)
        btn_get_state,              	 // 你的状态获取回调函数
        btn_prv_event				 // 你的事件处理回调函数
    );
	
	/*设置组合按键库配置*/
	ebtn_set_config(EBTN_CFG_COMBO_PRIORITY);
	
	// --- 配置组合键 (如果使用了组合键) ---
    // 1. 找到参与组合的普通按键的内部索引 (Index)
    int key1_index = ebtn_get_btn_index_by_key_id(1); // 获取 KEY1 (ID=1) 的内部索引
    int key2_index = ebtn_get_btn_index_by_key_id(2);
    int key3_index = ebtn_get_btn_index_by_key_id(3);
    int key4_index = ebtn_get_btn_index_by_key_id(4);
    int key5_index = ebtn_get_btn_index_by_key_id(5);
    int key6_index = ebtn_get_btn_index_by_key_id(6);

    // 2. 将这些索引对应的按键添加到组合键定义中
    //    确保索引有效 (>= 0)
    if (key1_index >= 0 && key2_index >= 0)
	{
        // 假设 static_combos[0] 是我们定义的 ID=101 的组合键
        ebtn_combo_btn_add_btn_by_idx(&static_combos[0], key1_index); // 将 KEY1 添加到组合键
        ebtn_combo_btn_add_btn_by_idx(&static_combos[0], key2_index); // 将 KEY2 添加到组合键
    }
	if(key1_index >= 0 && key3_index >= 0)
	{
		ebtn_combo_btn_add_btn_by_idx(&static_combos[1], key1_index); // 将 KEY1 添加到组合键
        ebtn_combo_btn_add_btn_by_idx(&static_combos[1], key3_index); // 将 KEY3 添加到组合键
	}
	if(key1_index >= 0 && key4_index >= 0)
	{
		ebtn_combo_btn_add_btn_by_idx(&static_combos[2], key1_index); // 将 KEY1 添加到组合键
        ebtn_combo_btn_add_btn_by_idx(&static_combos[2], key4_index); // 将 KEY4 添加到组合键
	}
	if(key5_index >= 0 && key6_index >= 0)
	{
		ebtn_combo_btn_add_btn_by_idx(&static_combos[3], key5_index); // 将 KEY1 添加到组合键
        ebtn_combo_btn_add_btn_by_idx(&static_combos[3], key6_index); // 将 KEY4 添加到组合键
	}
    return 0; // 初始化成功
}


/* 6. 周期性调用处理函数 */
void btn_task(void)
{
	ebtn_process(HAL_GetTick());
}



