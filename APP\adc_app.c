#include "adc_app.h"


// --- 宏定义和外部变量 ---
#define BUFFER_SIZE 1000        // DMA 缓冲区大小 (总点数)

// --- 全局变量 ---
uint32_t adc_val_buffer[BUFFER_SIZE]; // DMA 目标缓冲区 (存储原始 ADC 数据)
uint32_t sin_val_buffer[BUFFER_SIZE / 2]; // 用于存储处理后的 ADC 数据
uint8_t AdcConvEnd = 0;             // ADC 转换完成标志 (一个块完成)


// --- ADC的DMA初始化函数 (在 main 或外设初始化后调用) ---
void adc_tim_dma_init(void)
{
    // 启动 ADC 的 DMA 传输，请求 BUFFER_SIZE 个数据点
    // 且 DMA 配置为 Normal 模式
    HAL_ADC_Start_DMA(&hadc1, (uint32_t *)adc_val_buffer, BUFFER_SIZE);

    // 显式禁用 DMA 半传输中断 (如果不需要处理半满事件)
    __HAL_DMA_DISABLE_IT(&hdma_adc1, DMA_IT_HT);

    // 注意：如果使用定时器触发，需要在此处或之前启动定时器
    HAL_TIM_Base_Start(&htim3);
}


// --- ADC 转换完成回调函数 (由 DMA TC 中断触发) ---
// 当 DMA 完成整个缓冲区的传输 (Normal 模式下传输 BUFFER_SIZE 个点) 时触发
void HAL_ADC_ConvCpltCallback(ADC_HandleTypeDef* hadc)
{
    // 检查是否是由我们关心的 ADC (hadc1) 触发的
    if (hadc->Instance == ADC1) // 或 if(hadc == &hadc1)
    {
        HAL_ADC_Stop_DMA(hadc);

        // 设置转换完成标志，通知后台任务数据已准备好
        AdcConvEnd = 1;
    }
}


// --- 后台处理任务 (在主循环或低优先级任务中调用) ---
void adc_task(void)
{		
	// 检查转换完成标志
    if (AdcConvEnd)
    {
        // 处理数据: 从原始 ADC 缓冲区提取数据到 sin_val_buffer
        // 示例逻辑：提取扫描转换中第二个通道的数据
        for(uint16_t i = 0; i < BUFFER_SIZE / 2; i++)
        {
            // 假设 adc_val_buffer[0] 是通道1, adc_val_buffer[1] 是通道2, ...
            sin_val_buffer[i] = adc_val_buffer[i * 2 + 1];
        }
		
		// 打印处理后的数据
		for(uint16_t i = 0; i < BUFFER_SIZE / 2; i++)
		{
			my_printf(&huart1, "%d\r\n", (int)sin_val_buffer[i]);
		}
        
        // 清理处理后的缓冲区 (可选)
        memset(sin_val_buffer, 0, sizeof(uint32_t) * (BUFFER_SIZE / 2));

        // 清除转换完成标志，准备下一次采集
        AdcConvEnd = 0;

        // 重新启动 ADC 的 DMA 传输，采集下一个数据块
        // 注意: 需要确保 ADC 状态适合重启 (例如没有错误)
        HAL_ADC_Start_DMA(&hadc1, (uint32_t *)adc_val_buffer, BUFFER_SIZE);
        // 再次禁用半传输中断
        __HAL_DMA_DISABLE_IT(&hdma_adc1, DMA_IT_HT);
    }
}


