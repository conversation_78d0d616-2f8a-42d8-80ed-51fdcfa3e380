#ifndef _MYDEFINE_H
#define _MYDEFINE_H

#include "main.h"
#include "scheduler.h"

#include "ringbuffer.h"

#include "stdlib.h"
#include "stdarg.h"
#include "string.h"
#include "stdio.h"

#include "led_app.h"
#include "btn_app.h"
#include "usart_app.h"
#include "adc_app.h"
#include "dac_app.h"


extern uint8_t ucLed[6];  // LED 状态数组
extern uint8_t breath_enable;

extern UART_HandleTypeDef huart1;
extern DMA_HandleTypeDef hdma_usart1_rx;
extern uint8_t uart_rx_dma_buffer[128];
extern struct rt_ringbuffer uart_ringbuffer;
extern uint8_t ringbuffer_pool[128];

extern TIM_HandleTypeDef htim3;
extern TIM_HandleTypeDef htim4;
extern DAC_HandleTypeDef hdac;

extern DMA_HandleTypeDef hdma_adc1; // 假设这是 ADC1 对应的 DMA 句柄
extern ADC_HandleTypeDef hadc1;    // ADC1 句柄


#endif

